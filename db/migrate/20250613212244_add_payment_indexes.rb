class AddPaymentIndexes < ActiveRecord::Migration[7.1]
  def change
    # Add composite index for payment queries by booking and status
    add_index :payments, [:booking_id, :status], name: 'index_payments_on_booking_and_status'

    # Add index for payment method queries
    add_index :payments, :payment_method, name: 'index_payments_on_payment_method'

    # Add composite index for date range queries
    add_index :payments, [:created_at, :status], name: 'index_payments_on_created_at_and_status'

    # Add index for amount queries (useful for reporting)
    add_index :payments, :amount, name: 'index_payments_on_amount'
  end
end
