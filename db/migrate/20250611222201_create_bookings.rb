class CreateBookings < ActiveRecord::Migration[7.1]
  def change
    create_table :bookings do |t|
      t.references :user, null: false, foreign_key: true
      t.references :room, null: false, foreign_key: true
      t.date :check_in_date
      t.date :check_out_date
      t.decimal :total_amount
      t.string :payment_status
      t.string :booking_status
      t.integer :guest_count
      t.text :special_requests
      t.string :payment_reference

      t.timestamps
    end
    add_index :bookings, :payment_reference
    add_index :bookings, :check_in_date
    add_index :bookings, :check_out_date
  end
end
