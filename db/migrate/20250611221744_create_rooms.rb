class CreateRooms < ActiveRecord::Migration[7.1]
  def change
    create_table :rooms do |t|
      t.string :room_number
      t.string :room_type
      t.decimal :price_per_night
      t.integer :capacity
      t.text :amenities, array: true, default: []
      t.boolean :is_available
      t.text :description

      t.timestamps
    end
    add_index :rooms, :room_number
    add_index :rooms, :amenities, using: 'gin'
  end
end
