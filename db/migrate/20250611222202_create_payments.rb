class CreatePayments < ActiveRecord::Migration[7.1]
  def change
    create_table :payments do |t|
      t.references :booking, null: false, foreign_key: true
      t.decimal :amount
      t.string :currency
      t.string :payment_method
      t.string :payment_reference
      t.string :status

      t.timestamps
    end
    add_index :payments, :payment_reference
    add_index :payments, :status
  end
end
