# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

puts "🌱 Starting to seed the database..."

# Create Admin User
admin = User.find_or_create_by!(email: '<EMAIL>') do |user|
  user.password = 'password123'
  user.password_confirmation = 'password123'
  user.full_name = 'Admin User'
  user.phone = '+1234567890'
  user.role = 'admin'
end

puts "✅ Admin user created: #{admin.email}"

# Create Customer Users
customers = [
  {
    email: '<EMAIL>',
    full_name: '<PERSON>',
    phone: '+1234567891',
    password: 'password123'
  },
  {
    email: '<EMAIL>',
    full_name: '<PERSON>',
    phone: '+1234567892',
    password: 'password123'
  },
  {
    email: '<EMAIL>',
    full_name: '<PERSON>',
    phone: '+1234567893',
    password: 'password123'
  },
  {
    email: '<EMAIL>',
    full_name: '<PERSON>',
    phone: '+1234567894',
    password: 'password123'
  },
  {
    email: '<EMAIL>',
    full_name: '<PERSON>',
    phone: '+1234567895',
    password: 'password123'
  }
]

customer_users = []
customers.each do |customer_data|
  customer = User.find_or_create_by!(email: customer_data[:email]) do |user|
    user.password = customer_data[:password]
    user.password_confirmation = customer_data[:password]
    user.full_name = customer_data[:full_name]
    user.phone = customer_data[:phone]
    user.role = 'customer'
  end
  customer_users << customer
  puts "✅ Customer created: #{customer.full_name}"
end

# Create Rooms
rooms_data = [
  {
    room_number: '101',
    room_type: 'Standard Single',
    price_per_night: 75.00,
    capacity: 1,
    amenities: ['WiFi', 'Air Conditioning', 'TV', 'Private Bathroom'],
    is_available: true,
    description: 'Cozy single room perfect for solo travelers with all basic amenities.'
  },
  {
    room_number: '102',
    room_type: 'Standard Double',
    price_per_night: 120.00,
    capacity: 2,
    amenities: ['WiFi', 'Air Conditioning', 'TV', 'Private Bathroom', 'Mini Fridge'],
    is_available: true,
    description: 'Comfortable double room with modern amenities for couples or friends.'
  },
  {
    room_number: '201',
    room_type: 'Deluxe Suite',
    price_per_night: 200.00,
    capacity: 4,
    amenities: ['WiFi', 'Air Conditioning', 'TV', 'Private Bathroom', 'Mini Fridge', 'Balcony', 'Room Service'],
    is_available: true,
    description: 'Spacious deluxe suite with balcony and premium amenities for families.'
  },
  {
    room_number: '202',
    room_type: 'Executive Suite',
    price_per_night: 300.00,
    capacity: 4,
    amenities: ['WiFi', 'Air Conditioning', 'TV', 'Private Bathroom', 'Mini Fridge', 'Balcony', 'Room Service', 'Jacuzzi', 'Work Desk'],
    is_available: true,
    description: 'Luxury executive suite with jacuzzi and work area for business travelers.'
  },
  {
    room_number: '301',
    room_type: 'Family Room',
    price_per_night: 180.00,
    capacity: 6,
    amenities: ['WiFi', 'Air Conditioning', 'TV', 'Private Bathroom', 'Mini Fridge', 'Kitchenette'],
    is_available: true,
    description: 'Large family room with kitchenette, perfect for extended stays.'
  },
  {
    room_number: '302',
    room_type: 'Standard Single',
    price_per_night: 75.00,
    capacity: 1,
    amenities: ['WiFi', 'Air Conditioning', 'TV', 'Private Bathroom'],
    is_available: false,
    description: 'Cozy single room currently under maintenance.'
  },
  {
    room_number: '401',
    room_type: 'Penthouse Suite',
    price_per_night: 500.00,
    capacity: 8,
    amenities: ['WiFi', 'Air Conditioning', 'TV', 'Private Bathroom', 'Mini Fridge', 'Balcony', 'Room Service', 'Jacuzzi', 'Work Desk', 'Kitchen', 'Living Room'],
    is_available: true,
    description: 'Luxurious penthouse suite with full kitchen and living area.'
  }
]

created_rooms = []
rooms_data.each do |room_data|
  room = Room.find_or_create_by!(room_number: room_data[:room_number]) do |r|
    r.room_type = room_data[:room_type]
    r.price_per_night = room_data[:price_per_night]
    r.capacity = room_data[:capacity]
    r.amenities = room_data[:amenities]
    r.is_available = room_data[:is_available]
    r.description = room_data[:description]
  end
  created_rooms << room
  puts "✅ Room created: #{room.room_number} - #{room.room_type}"
end

# Create Bookings
bookings_data = [
  {
    user: customer_users[0], # John Doe
    room: created_rooms[0], # Room 101
    check_in_date: Date.current + 1.day,
    check_out_date: Date.current + 3.days,
    guest_count: 1,
    booking_status: 'confirmed',
    payment_status: 'paid',
    special_requests: 'Late check-in requested'
  },
  {
    user: customer_users[1], # Jane Smith
    room: created_rooms[1], # Room 102
    check_in_date: Date.current + 5.days,
    check_out_date: Date.current + 7.days,
    guest_count: 2,
    booking_status: 'confirmed',
    payment_status: 'pending',
    special_requests: 'Extra towels needed'
  },
  {
    user: customer_users[2], # Mike Johnson
    room: created_rooms[2], # Room 201 - Deluxe Suite
    check_in_date: Date.current + 10.days,
    check_out_date: Date.current + 14.days,
    guest_count: 3,
    booking_status: 'confirmed',
    payment_status: 'paid',
    special_requests: 'Anniversary celebration - flowers requested'
  },
  {
    user: customer_users[3], # Sarah Wilson
    room: created_rooms[4], # Room 301 - Family Room
    check_in_date: Date.current + 20.days,
    check_out_date: Date.current + 25.days,
    guest_count: 5,
    booking_status: 'pending',
    payment_status: 'pending',
    special_requests: 'Family with children - crib needed'
  },
  {
    user: customer_users[4], # David Brown
    room: created_rooms[6], # Room 401 - Penthouse Suite
    check_in_date: Date.current + 30.days,
    check_out_date: Date.current + 33.days,
    guest_count: 6,
    booking_status: 'confirmed',
    payment_status: 'partial',
    special_requests: 'Business meeting setup required'
  },
  # Past bookings
  {
    user: customer_users[0], # John Doe
    room: created_rooms[1], # Room 102
    check_in_date: Date.current - 10.days,
    check_out_date: Date.current - 7.days,
    guest_count: 2,
    booking_status: 'completed',
    payment_status: 'paid',
    special_requests: 'None'
  },
  {
    user: customer_users[2], # Mike Johnson
    room: created_rooms[3], # Room 202 - Executive Suite
    check_in_date: Date.current - 20.days,
    check_out_date: Date.current - 17.days,
    guest_count: 2,
    booking_status: 'completed',
    payment_status: 'paid',
    special_requests: 'Business trip - early breakfast requested'
  }
]

created_bookings = []
bookings_data.each_with_index do |booking_data, index|
  # Calculate total amount based on room price and nights
  nights = (booking_data[:check_out_date] - booking_data[:check_in_date]).to_i
  total_amount = booking_data[:room].price_per_night * nights

  booking = Booking.find_or_create_by!(
    user: booking_data[:user],
    room: booking_data[:room],
    check_in_date: booking_data[:check_in_date],
    check_out_date: booking_data[:check_out_date]
  ) do |b|
    b.guest_count = booking_data[:guest_count]
    b.total_amount = total_amount
    b.booking_status = booking_data[:booking_status]
    b.payment_status = booking_data[:payment_status]
    b.special_requests = booking_data[:special_requests]
    b.payment_reference = "BK#{Date.current.strftime('%Y%m%d')}#{index + 1}"
  end

  created_bookings << booking
  puts "✅ Booking created: #{booking.user.full_name} - Room #{booking.room.room_number} (#{booking.booking_status})"
end

# Create Payments
payment_methods = ['paystack', 'bank_transfer', 'cash', 'card']
payment_statuses = ['pending', 'completed', 'failed', 'refunded']

created_bookings.each_with_index do |booking, index|
  case booking.payment_status
  when 'paid'
    # Create completed payment
    payment = Payment.find_or_create_by!(
      booking: booking,
      payment_reference: "PAY#{Date.current.strftime('%Y%m%d')}#{booking.id}#{index + 1}"
    ) do |p|
      p.amount = booking.total_amount
      p.currency = 'NGN'
      p.payment_method = payment_methods.sample
      p.status = 'completed'
    end
    puts "✅ Payment created: #{payment.payment_reference} - #{payment.status} (₦#{payment.amount})"

  when 'partial'
    # Create partial payment
    partial_amount = (booking.total_amount * 0.5).round(2)
    payment = Payment.find_or_create_by!(
      booking: booking,
      payment_reference: "PAY#{Date.current.strftime('%Y%m%d')}#{booking.id}#{index + 1}"
    ) do |p|
      p.amount = partial_amount
      p.currency = 'NGN'
      p.payment_method = 'paystack'
      p.status = 'completed'
    end
    puts "✅ Partial payment created: #{payment.payment_reference} - #{payment.status} (₦#{payment.amount})"

  when 'pending'
    # Create pending payment
    payment = Payment.find_or_create_by!(
      booking: booking,
      payment_reference: "PAY#{Date.current.strftime('%Y%m%d')}#{booking.id}#{index + 1}"
    ) do |p|
      p.amount = booking.total_amount
      p.currency = 'NGN'
      p.payment_method = 'paystack'
      p.status = 'pending'
    end
    puts "✅ Pending payment created: #{payment.payment_reference} - #{payment.status} (₦#{payment.amount})"
  end
end

# Create some additional sample payments with different statuses
sample_payments = [
  {
    booking: created_bookings[0],
    amount: 50.00,
    currency: 'NGN',
    payment_method: 'paystack',
    status: 'failed',
    reference_suffix: 'FAIL'
  },
  {
    booking: created_bookings[1],
    amount: 100.00,
    currency: 'NGN',
    payment_method: 'bank_transfer',
    status: 'refunded',
    reference_suffix: 'REF'
  }
]

sample_payments.each_with_index do |payment_data, index|
  payment = Payment.create!(
    booking: payment_data[:booking],
    amount: payment_data[:amount],
    currency: payment_data[:currency],
    payment_method: payment_data[:payment_method],
    payment_reference: "PAY#{Date.current.strftime('%Y%m%d')}#{payment_data[:reference_suffix]}#{index + 1}",
    status: payment_data[:status]
  )
  puts "✅ Sample payment created: #{payment.payment_reference} - #{payment.status} (₦#{payment.amount})"
end

puts "\n🎉 Database seeding completed successfully!"
puts "📊 Summary:"
puts "   - Users: #{User.count} (#{User.where(role: 'admin').count} admin, #{User.where(role: 'customer').count} customers)"
puts "   - Rooms: #{Room.count} (#{Room.where(is_available: true).count} available)"
puts "   - Bookings: #{Booking.count}"
puts "   - Payments: #{Payment.count}"
puts "\n🔐 Admin Login:"
puts "   Email: <EMAIL>"
puts "   Password: password123"
puts "\n👤 Sample Customer Login:"
puts "   Email: <EMAIL>"
puts "   Password: password123"
