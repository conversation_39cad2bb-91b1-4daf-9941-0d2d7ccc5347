# Frontend Integration Example

This document provides examples of how to integrate the Paystack payment system with a frontend application.

## JavaScript/React Example

### 1. Initialize Payment

```javascript
// PaymentService.js
class PaymentService {
  constructor(baseURL, authToken) {
    this.baseURL = baseURL;
    this.authToken = authToken;
  }

  async initializePayment(bookingId) {
    try {
      const response = await fetch(`${this.baseURL}/payments/initialize_paystack`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`
        },
        body: JSON.stringify({
          booking_id: bookingId
        })
      });

      const data = await response.json();
      
      if (data.status.code === 200) {
        return {
          success: true,
          data: data.data
        };
      } else {
        return {
          success: false,
          message: data.status.message,
          errors: data.errors
        };
      }
    } catch (error) {
      return {
        success: false,
        message: 'Network error occurred',
        errors: [error.message]
      };
    }
  }

  async verifyPayment(reference) {
    try {
      const response = await fetch(`${this.baseURL}/payments/verify/${reference}`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        }
      });

      const data = await response.json();
      return data;
    } catch (error) {
      return {
        success: false,
        message: 'Verification failed',
        errors: [error.message]
      };
    }
  }
}
```

### 2. React Payment Component

```jsx
// PaymentComponent.jsx
import React, { useState } from 'react';

const PaymentComponent = ({ bookingId, onPaymentSuccess, onPaymentError }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const paymentService = new PaymentService(
    process.env.REACT_APP_API_URL,
    localStorage.getItem('authToken')
  );

  const handlePayment = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await paymentService.initializePayment(bookingId);
      
      if (result.success) {
        // Redirect to Paystack checkout
        window.location.href = result.data.authorization_url;
      } else {
        setError(result.message);
        onPaymentError(result);
      }
    } catch (error) {
      setError('Payment initialization failed');
      onPaymentError({ message: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="payment-component">
      <h3>Complete Your Payment</h3>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <button 
        onClick={handlePayment}
        disabled={loading}
        className="pay-button"
      >
        {loading ? 'Processing...' : 'Pay Now'}
      </button>
    </div>
  );
};

export default PaymentComponent;
```

### 3. Payment Verification Page

```jsx
// PaymentVerificationPage.jsx
import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';

const PaymentVerificationPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState('verifying');
  const [paymentData, setPaymentData] = useState(null);

  const paymentService = new PaymentService(
    process.env.REACT_APP_API_URL,
    localStorage.getItem('authToken')
  );

  useEffect(() => {
    const verifyPayment = async () => {
      const reference = searchParams.get('reference');
      
      if (!reference) {
        setStatus('error');
        return;
      }

      try {
        const result = await paymentService.verifyPayment(reference);
        
        if (result.status.code === 200) {
          setStatus('success');
          setPaymentData(result.data);
          
          // Redirect to success page after 3 seconds
          setTimeout(() => {
            navigate('/bookings');
          }, 3000);
        } else {
          setStatus('failed');
          setPaymentData(result.data);
        }
      } catch (error) {
        setStatus('error');
      }
    };

    verifyPayment();
  }, [searchParams, navigate]);

  const renderContent = () => {
    switch (status) {
      case 'verifying':
        return (
          <div className="verification-loading">
            <div className="spinner"></div>
            <p>Verifying your payment...</p>
          </div>
        );
      
      case 'success':
        return (
          <div className="verification-success">
            <div className="success-icon">✅</div>
            <h2>Payment Successful!</h2>
            <p>Your booking has been confirmed.</p>
            <div className="payment-details">
              <p><strong>Amount:</strong> ₦{paymentData?.payment?.amount}</p>
              <p><strong>Reference:</strong> {paymentData?.payment?.payment_reference}</p>
            </div>
            <p>Redirecting to your bookings...</p>
          </div>
        );
      
      case 'failed':
        return (
          <div className="verification-failed">
            <div className="error-icon">❌</div>
            <h2>Payment Failed</h2>
            <p>Your payment could not be processed.</p>
            <button onClick={() => navigate('/bookings')}>
              Return to Bookings
            </button>
          </div>
        );
      
      case 'error':
        return (
          <div className="verification-error">
            <div className="error-icon">⚠️</div>
            <h2>Verification Error</h2>
            <p>Unable to verify payment status.</p>
            <button onClick={() => navigate('/bookings')}>
              Return to Bookings
            </button>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="payment-verification-page">
      {renderContent()}
    </div>
  );
};

export default PaymentVerificationPage;
```

### 4. CSS Styles

```css
/* PaymentStyles.css */
.payment-component {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.pay-button {
  width: 100%;
  padding: 12px;
  background: #00C851;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
}

.pay-button:hover:not(:disabled) {
  background: #00A041;
}

.pay-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error-message {
  background: #ffebee;
  color: #c62828;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.payment-verification-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #f5f5f5;
}

.verification-loading,
.verification-success,
.verification-failed,
.verification-error {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  max-width: 400px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.success-icon,
.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.payment-details {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin: 20px 0;
  text-align: left;
}
```

## Usage in Your Application

1. **Install the payment component** in your booking flow
2. **Set up routing** for the verification page
3. **Configure environment variables** for your API URL
4. **Handle authentication** by storing JWT tokens
5. **Add error handling** for network failures and payment errors

## Environment Variables

```bash
# .env
REACT_APP_API_URL=http://localhost:3000
```

## Testing

Use Paystack test cards for development:
- **Success:** ****************, CVV: 408, PIN: 0000
- **Failure:** ****************, CVV: 408, PIN: 1111
