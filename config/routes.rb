Rails.application.routes.draw do
  get 'reviews/index'
  get 'reviews/show'
  get 'reviews/create'
  get 'reviews/update'
  get 'reviews/destroy'
  devise_for :users, path: '', path_names: {
    sign_in: 'user/login',
    sign_out: 'user/logout',
    registration: 'user/signup',
  },
  controllers: {
    sessions: 'users/sessions',
    registrations: 'users/registrations',
  }

  # User management routes
  resources :users, only: [:index, :show, :update, :destroy]

  # Room routes
  resources :rooms do
    # Nested route for room-specific reviews
    resources :reviews, only: [:index]
    resources :bookings, only: [:index, :create]
  end

  # Booking routes
  resources :bookings do
    # Nested routes for booking-related resources
    resources :payments, only: [:index, :create, :show, :update]
    resources :reviews, only: [:create]
  end

  # Payment routes
  resources :payments, only: [:index, :show, :update, :destroy] do
    member do
      post :refund_payment
    end
  end

  # Paystack payment routes
  post '/payments/initialize_paystack', to: 'payments#initialize_paystack_payment'
  get '/payments/verify/:reference', to: 'payments#verify_paystack_payment', as: :verify_payment
  post '/payments/webhook', to: 'payments#webhook'

  # Review routes
  resources :reviews, only: [:index, :show, :update, :destroy]

  # Additional useful routes
  get '/my/bookings', to: 'bookings#index', defaults: { user_scope: 'current' }
  get '/my/payments', to: 'payments#index', defaults: { user_scope: 'current' }
  get '/my/reviews', to: 'reviews#index', defaults: { my_reviews: 'true' }

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Defines the root path route ("/")
  # root "posts#index"
end
