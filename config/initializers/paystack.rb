# Paystack Configuration
# 
# To set up Paystack credentials:
# 1. For development/test: Set environment variables
#    export PAYSTACK_PUBLIC_KEY="pk_test_your_public_key"
#    export PAYSTACK_SECRET_KEY="sk_test_your_secret_key"
#
# 2. For production: Use Rails credentials
#    rails credentials:edit
#    Add:
#    paystack_public_key: pk_live_your_public_key
#    paystack_secret_key: sk_live_your_secret_key

Rails.application.configure do
  # Validate that Paystack keys are configured
  if Rails.env.production?
    # In production, require credentials to be set
    unless Rails.application.credentials.paystack_secret_key && Rails.application.credentials.paystack_public_key
      Rails.logger.warn "⚠️  Paystack credentials not configured in Rails credentials"
    end
  else
    # In development/test, allow environment variables
    unless (Rails.application.credentials.paystack_secret_key || ENV['PAYSTACK_SECRET_KEY']) &&
           (Rails.application.credentials.paystack_public_key || ENV['PAYSTACK_PUBLIC_KEY'])
      Rails.logger.warn "⚠️  Paystack credentials not configured. Set PAYSTACK_PUBLIC_KEY and PAYSTACK_SECRET_KEY environment variables or use Rails credentials."
    end
  end
end

# Paystack configuration constants
module PaystackConfig
  # Test keys (replace with your actual test keys)
  TEST_PUBLIC_KEY = 'pk_test_your_test_public_key_here'.freeze
  TEST_SECRET_KEY = 'sk_test_your_test_secret_key_here'.freeze
  
  # Supported currencies
  SUPPORTED_CURRENCIES = %w[NGN USD GHS ZAR KES].freeze
  
  # Default currency
  DEFAULT_CURRENCY = 'NGN'.freeze
  
  # Webhook events we handle
  WEBHOOK_EVENTS = %w[
    charge.success
    charge.failed
    refund.processed
    transfer.success
    transfer.failed
  ].freeze
  
  def self.public_key
    Rails.application.credentials.paystack_public_key || 
    ENV['PAYSTACK_PUBLIC_KEY'] || 
    (Rails.env.development? ? TEST_PUBLIC_KEY : nil)
  end
  
  def self.secret_key
    Rails.application.credentials.paystack_secret_key || 
    ENV['PAYSTACK_SECRET_KEY'] || 
    (Rails.env.development? ? TEST_SECRET_KEY : nil)
  end
  
  def self.configured?
    public_key.present? && secret_key.present?
  end
end
