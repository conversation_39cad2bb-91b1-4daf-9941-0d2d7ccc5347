GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.1.5.1)
      actionpack (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (7.1.5.1)
      actionpack (= 7.1.5.1)
      activejob (= 7.1.5.1)
      activerecord (= 7.1.5.1)
      activestorage (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.1.5.1)
      actionpack (= 7.1.5.1)
      actionview (= 7.1.5.1)
      activejob (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (7.1.5.1)
      actionview (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (7.1.5.1)
      actionpack (= 7.1.5.1)
      activerecord (= 7.1.5.1)
      activestorage (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.1.5.1)
      activesupport (= 7.1.5.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (7.1.5.1)
      activesupport (= 7.1.5.1)
      globalid (>= 0.3.6)
    activemodel (7.1.5.1)
      activesupport (= 7.1.5.1)
    activerecord (7.1.5.1)
      activemodel (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      timeout (>= 0.4.0)
    activestorage (7.1.5.1)
      actionpack (= 7.1.5.1)
      activejob (= 7.1.5.1)
      activerecord (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      marcel (~> 1.0)
    activesupport (7.1.5.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    ast (2.4.3)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    builder (3.3.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-jwt (0.12.1)
      devise (~> 4.0)
      warden-jwt_auth (~> 0.10)
    drb (2.2.3)
    dry-auto_inject (1.1.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-configurable (1.3.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-core (1.1.0)
      concurrent-ruby (~> 1.0)
      logger
      zeitwerk (~> 2.6)
    erb (5.0.1)
    erubi (1.13.1)
    ffi (1.17.2-aarch64-linux-gnu)
    ffi (1.17.2-aarch64-linux-musl)
    ffi (1.17.2-arm-linux-gnu)
    ffi (1.17.2-arm-linux-musl)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    ffi (1.17.2-x86_64-linux-musl)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.12.2)
    jwt (2.10.1)
      base64
    language_server-protocol (********)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    msgpack (1.8.0)
    mutex_m (0.3.0)
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.8-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-musl)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    psych (5.2.6)
      date
      stringio
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-cors (3.0.0)
      logger
      rack (>= 3.0.14)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (7.1.5.1)
      actioncable (= 7.1.5.1)
      actionmailbox (= 7.1.5.1)
      actionmailer (= 7.1.5.1)
      actionpack (= 7.1.5.1)
      actiontext (= 7.1.5.1)
      actionview (= 7.1.5.1)
      activejob (= 7.1.5.1)
      activemodel (= 7.1.5.1)
      activerecord (= 7.1.5.1)
      activestorage (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      bundler (>= 1.15.0)
      railties (= 7.1.5.1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (7.1.5.1)
      actionpack (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rdoc (6.14.0)
      erb
      psych (>= 4.0.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.24.0)
      connection_pool
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rubocop (1.76.1)
      json (~> 2.3)
      language_server-protocol (~> ********)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.45.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= *******)
      prism (~> 1.4)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.4)
      ffi (~> 1.12)
      logger
    securerandom (0.4.1)
    stringio (3.1.7)
    thor (1.3.2)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    warden (1.2.9)
      rack (>= 2.0.9)
    warden-jwt_auth (0.11.0)
      dry-auto_inject (>= 0.8, < 2)
      dry-configurable (>= 0.13, < 2)
      jwt (~> 2.1)
      warden (~> 1.2)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin
  x86_64-linux
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  bootsnap
  debug
  devise (~> 4.9, >= 4.9.4)
  devise-jwt (~> 0.12.1)
  image_processing (~> 1.2)
  pg (~> 1.1)
  puma (>= 5.0)
  rack-cors
  rails (~> 7.1.3, >= *******)
  redis (>= 4.0.1)
  rubocop (>= 1.0, < 2.0)
  tzinfo-data

RUBY VERSION
   ruby 3.3.0p0

BUNDLED WITH
   2.5.14
