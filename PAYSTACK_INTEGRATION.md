# Paystack Payment Integration

This document explains how to use the Paystack payment integration in the GuestHouse Server application.

## Setup

### 1. Install Dependencies

The required gems are already added to the Gemfile:
```ruby
gem 'httparty', '~> 0.21'
```

Run bundle install:
```bash
bundle install
```

### 2. Configure Paystack Credentials

#### For Development/Testing:
Set environment variables:
```bash
export PAYSTACK_PUBLIC_KEY="pk_test_your_test_public_key"
export PAYSTACK_SECRET_KEY="sk_test_your_test_secret_key"
```

#### For Production:
Use Rails credentials:
```bash
rails credentials:edit
```

Add:
```yaml
paystack_public_key: pk_live_your_live_public_key
paystack_secret_key: sk_live_your_live_secret_key
```

### 3. Run Migrations

```bash
rails db:migrate
rails db:seed
```

## API Endpoints

### Initialize Payment
**POST** `/payments/initialize_paystack`

**Parameters:**
- `booking_id` (required): ID of the booking to pay for

**Headers:**
- `Authorization: Bearer <jwt_token>`

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Payment initialized successfully."
  },
  "data": {
    "authorization_url": "https://checkout.paystack.com/...",
    "access_code": "access_code_here",
    "reference": "BK1_1234567890_ABCD",
    "amount": 15000.0,
    "public_key": "pk_test_..."
  }
}
```

### Verify Payment
**GET** `/payments/verify/:reference`

**Headers:**
- `Authorization: Bearer <jwt_token>`

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Payment verified successfully."
  },
  "data": {
    "payment": {
      "id": 1,
      "amount": 15000.0,
      "status": "completed",
      "payment_reference": "BK1_1234567890_ABCD"
    },
    "transaction": {
      "status": "success",
      "reference": "BK1_1234567890_ABCD",
      "amount": 1500000
    }
  }
}
```

### Webhook Endpoint
**POST** `/payments/webhook`

This endpoint handles Paystack webhooks for automatic payment status updates.

**Supported Events:**
- `charge.success` - Payment completed
- `charge.failed` - Payment failed
- `refund.processed` - Refund processed

### Refund Payment
**POST** `/payments/:id/refund_payment`

**Parameters:**
- `amount` (optional): Partial refund amount
- `customer_note` (optional): Note for customer
- `merchant_note` (optional): Internal note

**Headers:**
- `Authorization: Bearer <jwt_token>` (Admin only)

## Usage Flow

### 1. Customer Initiates Payment

```javascript
// Frontend JavaScript example
const response = await fetch('/payments/initialize_paystack', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${userToken}`
  },
  body: JSON.stringify({
    booking_id: bookingId
  })
});

const data = await response.json();
if (data.status.code === 200) {
  // Redirect to Paystack checkout
  window.location.href = data.data.authorization_url;
}
```

### 2. Payment Verification

After payment, Paystack redirects to the callback URL. The application can verify the payment:

```javascript
// Verify payment on callback
const reference = new URLSearchParams(window.location.search).get('reference');
const response = await fetch(`/payments/verify/${reference}`, {
  headers: {
    'Authorization': `Bearer ${userToken}`
  }
});

const result = await response.json();
if (result.data.payment.status === 'completed') {
  // Payment successful
  showSuccessMessage();
} else {
  // Payment failed
  showErrorMessage();
}
```

### 3. Webhook Processing

Paystack automatically sends webhooks to `/payments/webhook` for real-time payment status updates. No manual intervention required.

## Payment Statuses

- `pending` - Payment initiated but not completed
- `completed` - Payment successful
- `failed` - Payment failed
- `refunded` - Payment refunded

## Security Features

- JWT authentication required for all payment operations
- Webhook signature verification
- User authorization (users can only pay for their own bookings)
- Admin-only refund operations

## Testing

Use Paystack test cards for testing:

**Successful Payment:**
- Card: ****************
- CVV: 408
- Expiry: Any future date
- PIN: 0000

**Failed Payment:**
- Card: ****************
- CVV: 408
- Expiry: Any future date
- PIN: 1111

## Error Handling

The integration includes comprehensive error handling for:
- Invalid API credentials
- Network failures
- Invalid payment references
- Insufficient permissions
- Webhook signature verification failures

## Logging

All payment operations are logged for debugging and audit purposes:
- Payment initialization
- Verification attempts
- Webhook events
- Refund operations
