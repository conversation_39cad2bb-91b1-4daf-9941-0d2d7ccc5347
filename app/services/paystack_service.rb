class PaystackService
  include HTTParty
  
  base_uri 'https://api.paystack.co'
  
  def initialize
    @secret_key = Rails.application.credentials.paystack_secret_key || ENV['PAYSTACK_SECRET_KEY']
    @public_key = Rails.application.credentials.paystack_public_key || ENV['PAYSTACK_PUBLIC_KEY']
    
    raise 'Paystack secret key not configured' unless @secret_key
  end
  
  # Initialize a payment transaction
  def initialize_payment(email:, amount:, reference: nil, callback_url: nil, metadata: {})
    options = {
      headers: {
        'Authorization' => "Bearer #{@secret_key}",
        'Content-Type' => 'application/json'
      },
      body: {
        email: email,
        amount: (amount * 100).to_i, # Convert to kobo (smallest currency unit)
        reference: reference,
        callback_url: callback_url,
        metadata: metadata
      }.compact.to_json
    }
    
    response = self.class.post('/transaction/initialize', options)
    handle_response(response)
  end
  
  # Verify a payment transaction
  def verify_payment(reference)
    options = {
      headers: {
        'Authorization' => "Bearer #{@secret_key}",
        'Content-Type' => 'application/json'
      }
    }
    
    response = self.class.get("/transaction/verify/#{reference}", options)
    handle_response(response)
  end
  
  # Get transaction details
  def get_transaction(transaction_id)
    options = {
      headers: {
        'Authorization' => "Bearer #{@secret_key}",
        'Content-Type' => 'application/json'
      }
    }
    
    response = self.class.get("/transaction/#{transaction_id}", options)
    handle_response(response)
  end
  
  # List transactions with optional filters
  def list_transactions(params = {})
    options = {
      headers: {
        'Authorization' => "Bearer #{@secret_key}",
        'Content-Type' => 'application/json'
      },
      query: params
    }
    
    response = self.class.get('/transaction', options)
    handle_response(response)
  end
  
  # Refund a transaction
  def refund_transaction(transaction_reference, amount: nil, currency: 'NGN', customer_note: nil, merchant_note: nil)
    options = {
      headers: {
        'Authorization' => "Bearer #{@secret_key}",
        'Content-Type' => 'application/json'
      },
      body: {
        transaction: transaction_reference,
        amount: amount ? (amount * 100).to_i : nil, # Convert to kobo if amount specified
        currency: currency,
        customer_note: customer_note,
        merchant_note: merchant_note
      }.compact.to_json
    }
    
    response = self.class.post('/refund', options)
    handle_response(response)
  end
  
  # Get public key for frontend
  def public_key
    @public_key
  end
  
  # Generate a unique payment reference
  def generate_reference(prefix = 'PAY')
    "#{prefix}_#{Time.current.to_i}_#{SecureRandom.hex(4).upcase}"
  end
  
  # Convert amount from kobo to naira
  def kobo_to_naira(amount_in_kobo)
    (amount_in_kobo.to_f / 100).round(2)
  end
  
  # Convert amount from naira to kobo
  def naira_to_kobo(amount_in_naira)
    (amount_in_naira.to_f * 100).to_i
  end
  
  private
  
  def handle_response(response)
    case response.code
    when 200, 201
      parsed_response = JSON.parse(response.body)
      if parsed_response['status']
        {
          success: true,
          data: parsed_response['data'],
          message: parsed_response['message']
        }
      else
        {
          success: false,
          message: parsed_response['message'] || 'Transaction failed',
          errors: parsed_response['errors'] || []
        }
      end
    when 400
      {
        success: false,
        message: 'Bad request - Invalid parameters',
        errors: [JSON.parse(response.body)['message']] rescue ['Bad request']
      }
    when 401
      {
        success: false,
        message: 'Unauthorized - Invalid API key',
        errors: ['Invalid API credentials']
      }
    when 404
      {
        success: false,
        message: 'Transaction not found',
        errors: ['Transaction not found']
      }
    when 500
      {
        success: false,
        message: 'Internal server error',
        errors: ['Paystack server error']
      }
    else
      {
        success: false,
        message: 'Unknown error occurred',
        errors: ["HTTP #{response.code}"]
      }
    end
  rescue JSON::ParserError
    {
      success: false,
      message: 'Invalid response from Paystack',
      errors: ['Invalid JSON response']
    }
  rescue StandardError => e
    {
      success: false,
      message: 'Service error',
      errors: [e.message]
    }
  end
end
