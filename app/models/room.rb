class Room < ApplicationRecord
  has_many_attached :images

  # Associations
  has_many :bookings, dependent: :destroy
  has_many :users, through: :bookings
  has_many :payments, through: :bookings

  # Validations
  validates :room_number, presence: true, uniqueness: true
  validates :room_type, :price_per_night, :capacity, presence: true
  validates :price_per_night, numericality: { greater_than: 0 }
  validates :capacity, numericality: { greater_than: 0 }
end
