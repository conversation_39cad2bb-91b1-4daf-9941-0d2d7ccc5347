class Booking < ApplicationRecord
  # Associations
  belongs_to :user
  belongs_to :room
  has_many :payments, dependent: :destroy

  # Validations
  validates :check_in_date, :check_out_date, :total_amount, :guest_count, presence: true
  validates :total_amount, numericality: { greater_than: 0 }
  validates :guest_count, numericality: { greater_than: 0 }
  validate :check_out_after_check_in
  validate :guest_count_within_room_capacity

  private

  def check_out_after_check_in
    return unless check_out_date.present? && check_in_date.present?

    errors.add(:check_out_date, 'must be after check-in date') if check_out_date <= check_in_date
  end

  def guest_count_within_room_capacity
    return unless guest_count.present? && room.present?

    errors.add(:guest_count, 'exceeds room capacity') if guest_count > room.capacity
  end
end
