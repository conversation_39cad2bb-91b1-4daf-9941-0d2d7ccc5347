class User < ApplicationRecord
  include Devise::JWT::RevocationStrategies::JTIMatcher
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable,
         :jwt_authenticatable, jwt_revocation_strategy: self

  # Associations
  has_many :bookings, dependent: :destroy
  has_many :payments, through: :bookings
  has_many :rooms, through: :bookings

  before_create :set_default_role

  validates :full_name, :phone, presence: true

  private

  def set_default_role
    self.role ||= 'customer'
  end
end
