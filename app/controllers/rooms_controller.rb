class RoomsController < ApplicationController
  skip_before_action :authenticate_user!, only: %i[index show]
  before_action :admin_required, only: %i[create update destroy]
  before_action :set_room, only: %i[show update destroy]

  # GET /rooms (Anyone can view available rooms)
  def index
    @rooms = if current_user&.role == 'admin'
               # Ad<PERSON> can see all rooms
               Room.all
             else
               # Non-admins only see available rooms
               Room.where(is_available: true)
             end

    # Apply filters if provided
    @rooms = @rooms.where(room_type: params[:room_type]) if params[:room_type].present?
    @rooms = @rooms.where('capacity >= ?', params[:min_capacity]) if params[:min_capacity].present?
    @rooms = @rooms.where('price_per_night <= ?', params[:max_price]) if params[:max_price].present?

    render json: @rooms.as_json(include: { images: { only: [:id], methods: [:url] } })
  end

  # GET /rooms/:id (Anyone can view room details)
  def show
    render json: @room.as_json(include: { images: { only: [:id], methods: [:url] } })
  end

  # POST /rooms (Admin only)
  def create
    @room = Room.new(room_params)

    if @room.save
      attach_images if params[:images].present?
      render json: {
        status: { code: 201, message: 'Room created successfully.' },
        data: @room.as_json(include: { images: { only: [:id], methods: [:url] } })
      }, status: :created
    else
      render json: {
        status: { code: 422, message: 'Room creation failed.' },
        errors: @room.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /rooms/:id (Admin only)
  def update
    if @room.update(room_params)
      attach_images if params[:images].present?
      render json: {
        status: { code: 200, message: 'Room updated successfully.' },
        data: @room.as_json(include: { images: { only: [:id], methods: [:url] } })
      }
    else
      render json: {
        status: { code: 422, message: 'Room update failed.' },
        errors: @room.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # DELETE /rooms/:id (Admin only)
  def destroy
    @room.destroy
    render json: {
      status: { code: 200, message: 'Room deleted successfully.' }
    }
  end

  private

  def set_room
    @room = Room.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Room not found' }, status: :not_found
  end

  def room_params
    params.require(:room).permit(:room_number, :room_type, :price_per_night,
                                 :capacity, :is_available, :description,
                                 amenities: [])
  end

  def attach_images
    @room.images.attach(params[:images])
  end
end
