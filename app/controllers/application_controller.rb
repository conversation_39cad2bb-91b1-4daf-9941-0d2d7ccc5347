class ApplicationController < ActionController::API
  before_action :authenticate_user!, except: [:index] # Allow viewing rooms without auth
  before_action :configure_permitted_parameters, if: :devise_controller?

  respond_to :json

  private

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: %i[full_name phone])
    devise_parameter_sanitizer.permit(:account_update, keys: %i[full_name phone])
  end

  def admin_required
    render json: { error: 'Admin access required' }, status: :forbidden unless current_user&.role == 'admin'
  end

  def owner_or_admin_required(resource_user)
    return if current_user&.role == 'admin' || current_user == resource_user

    render json: { error: 'Access denied' }, status: :forbidden
  end

  def current_user_resource_required(resource_user)
    return if current_user == resource_user

    render json: { error: 'You can only access your own resources' }, status: :forbidden
  end
end
