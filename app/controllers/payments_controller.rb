class PaymentsController < ApplicationController
  before_action :set_payment, only: %i[show update destroy verify]
  before_action :authorize_payment_access, only: [:show]
  before_action :admin_required, only: %i[update destroy]
  before_action :set_booking, only: %i[create initialize_paystack_payment]

  # GET /payments
  def index
    @payments = if current_user.role == 'admin'
                  # Admins can view all payments
                  Payment.includes(:booking, :user).all
                else
                  # Users can only view their own payments
                  Payment.joins(:booking).where(bookings: { user: current_user })
                    .includes(:booking, :user)
                end

    # Apply filters
    @payments = @payments.where(status: params[:status]) if params[:status].present?
    @payments = @payments.where(payment_method: params[:method]) if params[:method].present?
    @payments = @payments.where('created_at >= ?', params[:from_date]) if params[:from_date].present?

    render json: @payments.as_json(
      include: {
        booking: {
          only: %i[id check_in_date check_out_date],
          include: {
            room: { only: %i[id room_number room_type] },
            user: { only: %i[id full_name email] }
          }
        }
      }
    )
  end

  # GET /payments/:id
  def show
    render json: @payment.as_json(
      include: {
        booking: {
          only: %i[id check_in_date check_out_date total_amount],
          include: {
            room: { only: %i[id room_number room_type] },
            user: { only: %i[id full_name email phone] }
          }
        }
      }
    )
  end

  # POST /payments/initialize_paystack - Initialize Paystack payment
  def initialize_paystack_payment
    # Users can only create payments for their own bookings
    unless current_user.role == 'admin' || @booking.user == current_user
      return render json: { error: 'Access denied' }, status: :forbidden
    end

    # Check if booking already has a pending payment
    existing_payment = @booking.payments.where(status: 'pending', payment_method: 'paystack').first
    if existing_payment
      return render json: {
        status: { code: 400, message: 'Pending payment already exists for this booking.' },
        data: {
          payment_reference: existing_payment.payment_reference,
          amount: existing_payment.amount
        }
      }, status: :bad_request
    end

    paystack_service = PaystackService.new
    reference = paystack_service.generate_reference("BK#{@booking.id}")

    # Calculate amount (use remaining balance if partial payment exists)
    paid_amount = @booking.payments.where(status: 'completed').sum(:amount)
    remaining_amount = @booking.total_amount - paid_amount

    if remaining_amount <= 0
      return render json: {
        status: { code: 400, message: 'Booking is already fully paid.' }
      }, status: :bad_request
    end

    # Initialize payment with Paystack
    callback_url = "#{request.base_url}/payments/verify/#{reference}"
    metadata = {
      booking_id: @booking.id,
      user_id: current_user.id,
      room_number: @booking.room.room_number,
      check_in_date: @booking.check_in_date,
      check_out_date: @booking.check_out_date
    }

    result = paystack_service.initialize_payment(
      email: current_user.email,
      amount: remaining_amount,
      reference: reference,
      callback_url: callback_url,
      metadata: metadata
    )

    if result[:success]
      # Create payment record
      @payment = @booking.payments.create!(
        amount: remaining_amount,
        currency: 'NGN',
        payment_method: 'paystack',
        payment_reference: reference,
        status: 'pending'
      )

      render json: {
        status: { code: 200, message: 'Payment initialized successfully.' },
        data: {
          authorization_url: result[:data]['authorization_url'],
          access_code: result[:data]['access_code'],
          reference: reference,
          amount: remaining_amount,
          public_key: paystack_service.public_key
        }
      }
    else
      render json: {
        status: { code: 422, message: 'Payment initialization failed.' },
        errors: result[:errors] || [result[:message]]
      }, status: :unprocessable_entity
    end
  end

  # GET /payments/verify/:reference - Verify Paystack payment
  def verify_paystack_payment
    reference = params[:reference]
    payment = Payment.find_by(payment_reference: reference)

    unless payment
      return render json: {
        status: { code: 404, message: 'Payment not found.' }
      }, status: :not_found
    end

    # Users can only verify their own payments
    unless current_user.role == 'admin' || payment.booking.user == current_user
      return render json: { error: 'Access denied' }, status: :forbidden
    end

    paystack_service = PaystackService.new
    result = paystack_service.verify_payment(reference)

    if result[:success]
      transaction_data = result[:data]

      case transaction_data['status']
      when 'success'
        payment.update!(status: 'completed')

        # Update booking payment status if fully paid
        total_paid = payment.booking.payments.where(status: 'completed').sum(:amount)
        if total_paid >= payment.booking.total_amount
          payment.booking.update!(payment_status: 'paid')
        else
          payment.booking.update!(payment_status: 'partial')
        end

        render json: {
          status: { code: 200, message: 'Payment verified successfully.' },
          data: {
            payment: payment.as_json(include: { booking: { only: %i[id total_amount payment_status] } }),
            transaction: transaction_data
          }
        }
      when 'failed'
        payment.update!(status: 'failed')
        render json: {
          status: { code: 400, message: 'Payment failed.' },
          data: { payment: payment, transaction: transaction_data }
        }, status: :bad_request
      else
        render json: {
          status: { code: 200, message: 'Payment is still pending.' },
          data: { payment: payment, transaction: transaction_data }
        }
      end
    else
      render json: {
        status: { code: 422, message: 'Payment verification failed.' },
        errors: result[:errors] || [result[:message]]
      }, status: :unprocessable_entity
    end
  end

  # POST /payments
  def create
    # Users can only create payments for their own bookings
    unless current_user.role == 'admin' || @booking.user == current_user
      return render json: { error: 'Access denied' }, status: :forbidden
    end

    @payment = @booking.payments.new(payment_params)

    if @payment.save
      render json: {
        status: { code: 201, message: 'Payment created successfully.' },
        data: @payment.as_json(
          include: {
            booking: { only: %i[id total_amount] }
          }
        )
      }, status: :created
    else
      render json: {
        status: { code: 422, message: 'Payment creation failed.' },
        errors: @payment.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /payments/:id (Admin only)
  def update
    if @payment.update(payment_update_params)
      render json: {
        status: { code: 200, message: 'Payment updated successfully.' },
        data: @payment
      }
    else
      render json: {
        status: { code: 422, message: 'Payment update failed.' },
        errors: @payment.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # POST /payments/webhook - Handle Paystack webhooks
  def webhook
    # Verify webhook signature
    signature = request.headers['X-Paystack-Signature']
    payload = request.raw_post

    unless verify_webhook_signature(payload, signature)
      return render json: { error: 'Invalid signature' }, status: :unauthorized
    end

    event_data = JSON.parse(payload)
    event_type = event_data['event']
    data = event_data['data']

    case event_type
    when 'charge.success'
      handle_successful_payment(data)
    when 'charge.failed'
      handle_failed_payment(data)
    when 'refund.processed'
      handle_refund_processed(data)
    else
      Rails.logger.info "Unhandled webhook event: #{event_type}"
    end

    render json: { status: 'success' }
  rescue JSON::ParserError
    render json: { error: 'Invalid JSON' }, status: :bad_request
  rescue StandardError => e
    Rails.logger.error "Webhook error: #{e.message}"
    render json: { error: 'Internal server error' }, status: :internal_server_error
  end

  # POST /payments/:id/refund (Admin only - for Paystack refunds)
  def refund_payment
    unless @payment.payment_method == 'paystack' && @payment.status == 'completed'
      return render json: {
        status: { code: 400, message: 'Only completed Paystack payments can be refunded.' }
      }, status: :bad_request
    end

    paystack_service = PaystackService.new
    refund_amount = params[:amount]&.to_f || @payment.amount

    result = paystack_service.refund_transaction(
      @payment.payment_reference,
      amount: refund_amount,
      customer_note: params[:customer_note],
      merchant_note: params[:merchant_note]
    )

    if result[:success]
      @payment.update!(status: 'refunded')

      # Update booking payment status
      total_paid = @payment.booking.payments.where(status: 'completed').sum(:amount)
      if total_paid < @payment.booking.total_amount
        @payment.booking.update!(payment_status: 'partial')
      else
        @payment.booking.update!(payment_status: 'refunded')
      end

      render json: {
        status: { code: 200, message: 'Refund processed successfully.' },
        data: {
          payment: @payment,
          refund_data: result[:data]
        }
      }
    else
      render json: {
        status: { code: 422, message: 'Refund failed.' },
        errors: result[:errors] || [result[:message]]
      }, status: :unprocessable_entity
    end
  end

  # DELETE /payments/:id (Admin only - for manual refunds)
  def destroy
    @payment.update(status: 'refunded')

    # Update booking payment status
    total_paid = @payment.booking.payments.where(status: 'completed').sum(:amount)
    @payment.booking.update!(payment_status: 'partial') if total_paid < @payment.booking.total_amount

    render json: {
      status: { code: 200, message: 'Payment refunded successfully.' }
    }
  end

  private

  def set_payment
    @payment = Payment.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Payment not found' }, status: :not_found
  end

  def set_booking
    @booking = Booking.find(params[:booking_id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Booking not found' }, status: :not_found
  end

  def authorize_payment_access
    return if current_user.role == 'admin' || @payment.booking.user == current_user

    render json: { error: 'Access denied' }, status: :forbidden
  end

  def payment_params
    params.require(:payment).permit(:amount, :currency, :payment_method,
                                    :payment_reference, :status)
  end

  def payment_update_params
    params.require(:payment).permit(:status, :payment_reference)
  end

  # Webhook signature verification
  def verify_webhook_signature(payload, signature)
    secret_key = Rails.application.credentials.paystack_secret_key || ENV.fetch('PAYSTACK_SECRET_KEY', nil)
    expected_signature = OpenSSL::HMAC.hexdigest('sha512', secret_key, payload)
    ActiveSupport::SecurityUtils.secure_compare(signature, expected_signature)
  end

  # Handle successful payment webhook
  def handle_successful_payment(data)
    reference = data['reference']
    payment = Payment.find_by(payment_reference: reference)

    return unless payment && payment.status == 'pending'

    payment.update!(status: 'completed')

    # Update booking payment status
    total_paid = payment.booking.payments.where(status: 'completed').sum(:amount)
    if total_paid >= payment.booking.total_amount
      payment.booking.update!(payment_status: 'paid')
    else
      payment.booking.update!(payment_status: 'partial')
    end

    Rails.logger.info "Payment completed via webhook: #{reference}"
  end

  # Handle failed payment webhook
  def handle_failed_payment(data)
    reference = data['reference']
    payment = Payment.find_by(payment_reference: reference)

    return unless payment && payment.status == 'pending'

    payment.update!(status: 'failed')
    Rails.logger.info "Payment failed via webhook: #{reference}"
  end

  # Handle refund processed webhook
  def handle_refund_processed(data)
    reference = data['transaction_reference']
    payment = Payment.find_by(payment_reference: reference)

    return unless payment

    payment.update!(status: 'refunded')

    # Update booking payment status
    total_paid = payment.booking.payments.where(status: 'completed').sum(:amount)
    payment.booking.update!(payment_status: 'partial') if total_paid < payment.booking.total_amount

    Rails.logger.info "Refund processed via webhook: #{reference}"
  end
end
