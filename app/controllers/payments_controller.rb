class PaymentsController < ApplicationController
  before_action :set_payment, only: %i[show update destroy]
  before_action :authorize_payment_access, only: [:show]
  before_action :admin_required, only: %i[update destroy]

  # GET /payments
  def index
    @payments = if current_user.role == 'admin'
                  # Admins can view all payments
                  Payment.includes(:booking, :user).all
                else
                  # Users can only view their own payments
                  Payment.joins(:booking).where(bookings: { user: current_user })
                    .includes(:booking, :user)
                end

    # Apply filters
    @payments = @payments.where(status: params[:status]) if params[:status].present?
    @payments = @payments.where(payment_method: params[:method]) if params[:method].present?
    @payments = @payments.where('created_at >= ?', params[:from_date]) if params[:from_date].present?

    render json: @payments.as_json(
      include: {
        booking: {
          only: %i[id check_in_date check_out_date],
          include: {
            room: { only: %i[id room_number room_type] },
            user: { only: %i[id full_name email] }
          }
        }
      }
    )
  end

  # GET /payments/:id
  def show
    render json: @payment.as_json(
      include: {
        booking: {
          only: %i[id check_in_date check_out_date total_amount],
          include: {
            room: { only: %i[id room_number room_type] },
            user: { only: %i[id full_name email phone] }
          }
        }
      }
    )
  end

  # POST /payments
  def create
    @booking = Booking.find(params[:booking_id])

    # Users can only create payments for their own bookings
    unless current_user.role == 'admin' || @booking.user == current_user
      return render json: { error: 'Access denied' }, status: :forbidden
    end

    @payment = @booking.payments.new(payment_params)

    if @payment.save
      render json: {
        status: { code: 201, message: 'Payment created successfully.' },
        data: @payment.as_json(
          include: {
            booking: { only: %i[id total_amount] }
          }
        )
      }, status: :created
    else
      render json: {
        status: { code: 422, message: 'Payment creation failed.' },
        errors: @payment.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /payments/:id (Admin only)
  def update
    if @payment.update(payment_update_params)
      render json: {
        status: { code: 200, message: 'Payment updated successfully.' },
        data: @payment
      }
    else
      render json: {
        status: { code: 422, message: 'Payment update failed.' },
        errors: @payment.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # DELETE /payments/:id (Admin only - for refunds)
  def destroy
    @payment.update(status: 'refunded')
    render json: {
      status: { code: 200, message: 'Payment refunded successfully.' }
    }
  end

  private

  def set_payment
    @payment = Payment.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Payment not found' }, status: :not_found
  end

  def authorize_payment_access
    return if current_user.role == 'admin' || @payment.booking.user == current_user

    render json: { error: 'Access denied' }, status: :forbidden
  end

  def payment_params
    params.require(:payment).permit(:amount, :currency, :payment_method,
                                    :payment_reference, :status)
  end

  def payment_update_params
    params.require(:payment).permit(:status, :payment_reference)
  end
end
