class UsersController < ApplicationController
  before_action :admin_required, only: [:index]
  before_action :set_user, only: %i[show update destroy]
  before_action :authorize_user_access, only: %i[show update destroy]

  # GET /users (Admin only - view all users)
  def index
    @users = User.all
    render json: @users.as_json(except: %i[encrypted_password jti])
  end

  # GET /users/:id (Users can view own profile, admins can view any)
  def show
    render json: @user.as_json(except: %i[encrypted_password jti])
  end

  # PATCH/PUT /users/:id (Users can update own profile, admins can update any)
  def update
    if @user.update(user_params)
      render json: {
        status: { code: 200, message: 'Profile updated successfully.' },
        data: @user.as_json(except: %i[encrypted_password jti])
      }
    else
      render json: {
        status: { code: 422, message: 'Update failed.' },
        errors: @user.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # DELETE /users/:id (Users can delete own account, admins can delete any)
  def destroy
    @user.destroy
    render json: {
      status: { code: 200, message: 'Account deleted successfully.' }
    }
  end

  private

  def set_user
    @user = User.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'User not found' }, status: :not_found
  end

  def authorize_user_access
    owner_or_admin_required(@user)
  end

  def user_params
    params.require(:user).permit(:full_name, :phone, :email)
  end
end
