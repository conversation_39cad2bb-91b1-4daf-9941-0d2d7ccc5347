class BookingsController < ApplicationController
  before_action :set_booking, only: %i[show update destroy]
  before_action :authorize_booking_access, only: %i[show update destroy]

  # GET /bookings
  def index
    @bookings = if current_user.role == 'admin'
                  # Admins can view all bookings
                  Booking.includes(:user, :room, :payments).all
                else
                  # Users can only view their own bookings
                  current_user.bookings.includes(:room, :payments)
                end

    # Apply filters
    @bookings = @bookings.where(booking_status: params[:status]) if params[:status].present?
    @bookings = @bookings.where('check_in_date >= ?', params[:from_date]) if params[:from_date].present?
    @bookings = @bookings.where('check_out_date <= ?', params[:to_date]) if params[:to_date].present?

    render json: @bookings.as_json(
      include: {
        room: { only: %i[id room_number room_type price_per_night] },
        user: { only: %i[id full_name email] },
        payments: { only: %i[id amount status payment_method] }
      }
    )
  end

  # GET /bookings/:id
  def show
    render json: @booking.as_json(
      include: {
        room: { only: %i[id room_number room_type price_per_night] },
        user: { only: %i[id full_name email phone] },
        payments: { only: %i[id amount status payment_method created_at] }
      }
    )
  end

  # POST /bookings
  def create
    @booking = current_user.bookings.new(booking_params)

    if @booking.save
      render json: {
        status: { code: 201, message: 'Booking created successfully.' },
        data: @booking.as_json(
          include: {
            room: { only: %i[id room_number room_type price_per_night] }
          }
        )
      }, status: :created
    else
      render json: {
        status: { code: 422, message: 'Booking creation failed.' },
        errors: @booking.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /bookings/:id
  def update
    if @booking.update(booking_update_params)
      render json: {
        status: { code: 200, message: 'Booking updated successfully.' },
        data: @booking.as_json(
          include: {
            room: { only: %i[id room_number room_type price_per_night] }
          }
        )
      }
    else
      render json: {
        status: { code: 422, message: 'Booking update failed.' },
        errors: @booking.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # DELETE /bookings/:id
  def destroy
    @booking.destroy
    render json: {
      status: { code: 200, message: 'Booking cancelled successfully.' }
    }
  end

  private

  def set_booking
    @booking = Booking.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Booking not found' }, status: :not_found
  end

  def authorize_booking_access
    owner_or_admin_required(@booking.user)
  end

  def booking_params
    params.require(:booking).permit(:room_id, :check_in_date, :check_out_date,
                                    :total_amount, :guest_count, :special_requests,
                                    :booking_status, :payment_status)
  end

  def booking_update_params
    # Users can only update certain fields, admins can update all
    if current_user.role == 'admin'
      booking_params
    else
      params.require(:booking).permit(:check_in_date, :check_out_date,
                                      :guest_count, :special_requests)
    end
  end
end
