require 'test_helper'

class PaystackServiceTest < ActiveSupport::TestCase
  def setup
    @service = PaystackService.new
    # Mock the API keys for testing
    Rails.application.credentials.stubs(:paystack_secret_key).returns('sk_test_mock_key')
    Rails.application.credentials.stubs(:paystack_public_key).returns('pk_test_mock_key')
  end

  test "should generate unique payment reference" do
    ref1 = @service.generate_reference
    ref2 = @service.generate_reference
    
    assert_not_equal ref1, ref2
    assert ref1.start_with?('PAY_')
    assert ref2.start_with?('PAY_')
  end

  test "should generate reference with custom prefix" do
    ref = @service.generate_reference('BOOKING')
    assert ref.start_with?('BOOKING_')
  end

  test "should convert naira to kobo correctly" do
    assert_equal 10000, @service.naira_to_kobo(100.00)
    assert_equal 15050, @service.naira_to_kobo(150.50)
    assert_equal 1, @service.naira_to_kobo(0.01)
  end

  test "should convert kobo to naira correctly" do
    assert_equal 100.00, @service.kobo_to_naira(10000)
    assert_equal 150.50, @service.kobo_to_naira(15050)
    assert_equal 0.01, @service.kobo_to_naira(1)
  end

  test "should return public key" do
    assert_equal 'pk_test_mock_key', @service.public_key
  end

  test "should handle response with success status" do
    mock_response = mock('response')
    mock_response.stubs(:code).returns(200)
    mock_response.stubs(:body).returns({
      status: true,
      message: 'Success',
      data: { reference: 'test_ref' }
    }.to_json)

    result = @service.send(:handle_response, mock_response)
    
    assert result[:success]
    assert_equal 'Success', result[:message]
    assert_equal 'test_ref', result[:data]['reference']
  end

  test "should handle response with error status" do
    mock_response = mock('response')
    mock_response.stubs(:code).returns(400)
    mock_response.stubs(:body).returns({
      status: false,
      message: 'Bad request'
    }.to_json)

    result = @service.send(:handle_response, mock_response)
    
    assert_not result[:success]
    assert_equal 'Bad request - Invalid parameters', result[:message]
  end

  test "should handle unauthorized response" do
    mock_response = mock('response')
    mock_response.stubs(:code).returns(401)
    mock_response.stubs(:body).returns('Unauthorized')

    result = @service.send(:handle_response, mock_response)
    
    assert_not result[:success]
    assert_equal 'Unauthorized - Invalid API key', result[:message]
    assert_includes result[:errors], 'Invalid API credentials'
  end

  test "should handle not found response" do
    mock_response = mock('response')
    mock_response.stubs(:code).returns(404)
    mock_response.stubs(:body).returns('Not found')

    result = @service.send(:handle_response, mock_response)
    
    assert_not result[:success]
    assert_equal 'Transaction not found', result[:message]
  end

  test "should handle server error response" do
    mock_response = mock('response')
    mock_response.stubs(:code).returns(500)
    mock_response.stubs(:body).returns('Internal server error')

    result = @service.send(:handle_response, mock_response)
    
    assert_not result[:success]
    assert_equal 'Internal server error', result[:message]
  end

  test "should handle invalid JSON response" do
    mock_response = mock('response')
    mock_response.stubs(:code).returns(200)
    mock_response.stubs(:body).returns('invalid json')

    result = @service.send(:handle_response, mock_response)
    
    assert_not result[:success]
    assert_equal 'Invalid response from Paystack', result[:message]
  end
end
